#!/usr/bin/env python3
"""
测试统一通信契约传递修复
"""

import os
import re

def test_device_dispatcher_fix():
    """测试 device_dispatcher_node 修复"""
    print("🧪 测试 device_dispatcher_node 修复...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            # 1. 强制传递统一契约
            ('unified_contract = state.get(\'unified_communication_contract\')', 'device_dispatcher_node 获取统一契约'),
            ('if unified_contract is not None:', 'device_dispatcher_node 检查契约存在'),
            ('update_dict[\'unified_communication_contract\'] = unified_contract', 'device_dispatcher_node 强制传递契约'),
            
            # 2. 调试信息
            ('Explicitly passing unified_communication_contract with', 'device_dispatcher_node 传递调试信息'),
            ('unified_communication_contract is None in device_dispatcher_node', 'device_dispatcher_node 缺失警告'),
            
            # 3. 确保在状态清理之前设置
            ('# 6. 强制确保关键契约被传递（防止 None 值被过滤）', '契约传递注释'),
            ('# 7. 显式清理可能残留的状态', '状态清理注释更新'),
        ]
        
        passed = 0
        failed = []
        
        for pattern, description in checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
                failed.append(description)
        
        print(f"\n  📊 device_dispatcher_node 修复检查: {passed}/{len(checks)} 项通过")
        
        if failed:
            print("  ❌ 失败的检查项:")
            for item in failed:
                print(f"    - {item}")
        
        return len(failed) == 0
        
    except Exception as e:
        print(f"  ❌ device_dispatcher_node 修复测试失败: {e}")
        return False

def test_contract_flow_integrity():
    """测试契约流转完整性"""
    print("🧪 测试契约流转完整性...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查完整的契约流转链
        flow_checks = [
            # 1. plan_enrichment_node 生成和返回
            ('unified_communication_contract = {', 'plan_enrichment_node 生成契约'),
            ('"unified_communication_contract": unified_communication_contract', 'plan_enrichment_node 返回契约'),
            
            # 2. device_dispatcher_node 接收和传递
            ('"unified_communication_contract": state.get(\'unified_communication_contract\')', 'device_dispatcher_node persistent_state 包含契约'),
            ('update_dict[\'unified_communication_contract\'] = unified_contract', 'device_dispatcher_node 强制传递契约'),
            
            # 3. developer_node 接收和使用
            ('unified_contract = state.get(\'unified_communication_contract\', {})', 'developer_node 获取契约'),
            ('if not unified_contract:', 'developer_node 检查契约'),
            
            # 4. test_plan_designer_node 接收和使用
            ('unified_contract = state.get("unified_communication_contract", {})', 'test_plan_designer_node 获取契约'),
        ]
        
        passed = 0
        for pattern, description in flow_checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 契约流转完整性检查: {passed}/{len(flow_checks)} 项通过")
        return passed == len(flow_checks)
        
    except Exception as e:
        print(f"  ❌ 契约流转完整性测试失败: {e}")
        return False

def test_debug_information():
    """测试调试信息是否完整"""
    print("🧪 测试调试信息完整性...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查调试信息
        debug_checks = [
            # plan_enrichment_node 调试
            ('plan_enrichment_node: device_tasks length =', 'plan_enrichment_node 设备任务调试'),
            ('Device roles:', 'plan_enrichment_node 设备角色调试'),
            
            # device_dispatcher_node 调试
            ('device_dispatcher_node: queue length =', 'device_dispatcher_node 队列调试'),
            ('Next device:', 'device_dispatcher_node 下一设备调试'),
            ('Explicitly passing unified_communication_contract', 'device_dispatcher_node 契约传递调试'),
            
            # developer_node 调试
            ('unified_contract exists:', 'developer_node 契约存在调试'),
            ('device_task exists:', 'developer_node 设备任务调试'),
            ('device_role:', 'developer_node 设备角色调试'),
            ('topic_map keys:', 'developer_node 主题映射调试'),
            
            # test_plan_designer_node 调试
            ('current_device_task exists:', 'test_plan_designer_node 设备任务调试'),
        ]
        
        passed = 0
        for pattern, description in debug_checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 调试信息完整性检查: {passed}/{len(debug_checks)} 项通过")
        return passed >= 8  # 至少要有主要的调试信息
        
    except Exception as e:
        print(f"  ❌ 调试信息完整性测试失败: {e}")
        return False

def analyze_potential_remaining_issues():
    """分析可能剩余的问题"""
    print("🔍 分析可能剩余的问题...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 1. 检查是否还有其他地方可能丢失契约
        contract_usage = re.findall(r'unified_communication_contract.*=.*state\.get', content)
        print(f"  📊 发现 {len(contract_usage)} 处从 state 获取统一契约")
        
        # 2. 检查是否有地方可能覆盖契约
        contract_assignments = re.findall(r'unified_communication_contract.*=.*(?!state\.get)', content)
        if contract_assignments:
            issues.append(f"可能覆盖统一契约的代码: {len(contract_assignments)} 处")
        
        # 3. 检查状态重置是否影响契约
        reset_section = re.search(r'states_to_reset = \[(.*?)\]', content, re.DOTALL)
        if reset_section:
            reset_items = reset_section.group(1)
            if 'unified_communication_contract' in reset_items:
                issues.append("states_to_reset 包含 unified_communication_contract")
            else:
                print(f"  ✅ states_to_reset 不包含 unified_communication_contract")
        
        # 4. 检查是否有空字典赋值
        empty_dict_patterns = [
            r'unified_communication_contract.*=.*{}',
            r'unified_communication_contract.*=.*None'
        ]
        
        for pattern in empty_dict_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"可能清空统一契约的代码: {matches}")
        
        if issues:
            print(f"  ⚠️ 发现 {len(issues)} 个潜在问题:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print(f"  ✅ 未发现明显的潜在问题")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"  ❌ 潜在问题分析失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 测试统一通信契约传递修复...\n")
    
    tests = [
        ("device_dispatcher_node 修复", test_device_dispatcher_fix),
        ("契约流转完整性", test_contract_flow_integrity),
        ("调试信息完整性", test_debug_information),
        ("潜在问题分析", analyze_potential_remaining_issues)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 测试总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed >= 3:  # 前3项是关键的修复检查
        print("🎉 统一通信契约传递修复成功！")
        print("💡 现在 device_dispatcher_node 会强制确保 unified_communication_contract 被传递。")
        print("🔧 建议: 重新运行工作流来验证修复效果。")
        print("\n📝 预期结果:")
        print("  - developer_node 应该能收到 unified_communication_contract")
        print("  - 不再出现 'Missing unified_communication_contract' 错误")
        print("  - 调试信息会显示契约传递的详细过程")
    else:
        print("⚠️ 修复可能不完整，需要进一步检查。")

if __name__ == "__main__":
    main()
