#!/usr/bin/env python3
"""
简单的契约修复验证，不依赖环境变量
"""

import os
import re

def test_code_modifications():
    """测试代码修改是否正确应用"""
    print("🧪 测试代码修改...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            # 1. derive_payload_contract 修复
            ('NEW: 只要 unified_contract 存在，就绝不走 heuristic fallback', 'derive_payload_contract 注释更新'),
            ('device_topics.get("pub") or device_topics.get("sub") or [None]', 'derive_payload_contract 逻辑简化'),
            
            # 2. 主题合规性检查函数
            ('def _assert_topic_compliance', '主题合规性检查函数定义'),
            ('expected_topics = set()', '主题合规性检查逻辑'),
            
            # 3. 通信上下文改进
            ('<CommunicationContract>', '通信契约标签'),
            ('json.dumps(unified_contract, indent=2)', '契约JSON序列化'),
            ('<CommunicationInstructions>', '通信指令标签'),
            
            # 4. developer_node 中的验证
            ('_assert_topic_compliance(main_code or "", unified_contract)', '代码合规性检查调用'),
            ('Communication contract compliance check passed', '合规性检查成功日志'),
            
            # 5. 统一契约传递
            ('"unified_communication_contract": state.get(\'unified_communication_contract\')', '统一契约传递'),
            
            # 6. test_plan_designer_node 修复
            ('TEST PLAN DESIGNER V5: Creating test plan from Unified Communication Contract', 'test_plan_designer_node 更新')
        ]
        
        passed = 0
        failed = []
        
        for pattern, description in checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
                failed.append(description)
        
        print(f"\n  📊 代码修改检查: {passed}/{len(checks)} 项通过")
        
        if failed:
            print("  ❌ 失败的检查项:")
            for item in failed:
                print(f"    - {item}")
        
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ 代码修改测试失败: {e}")
        return False

def test_function_signatures():
    """测试函数签名是否正确"""
    print("🧪 测试函数签名...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数定义
        function_checks = [
            (r'def derive_payload_contract\(state\) -> dict:', 'derive_payload_contract 函数签名'),
            (r'def _assert_topic_compliance\(code: str, contract: dict\):', '_assert_topic_compliance 函数签名'),
            (r'def test_plan_designer_node\(state: AgentState\) -> Dict:', 'test_plan_designer_node 函数签名'),
        ]
        
        passed = 0
        for pattern, description in function_checks:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 函数签名检查: {passed}/{len(function_checks)} 项通过")
        return passed == len(function_checks)
        
    except Exception as e:
        print(f"  ❌ 函数签名测试失败: {e}")
        return False

def test_logic_flow():
    """测试逻辑流程是否正确"""
    print("🧪 测试逻辑流程...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查逻辑流程
        logic_checks = [
            # derive_payload_contract 逻辑
            ('if unified_contract and device_role:', 'derive_payload_contract 统一契约检查'),
            ('topic = (device_topics.get("pub") or device_topics.get("sub") or [None])[0]', 'derive_payload_contract 主题选择'),
            ('print("  -> WARNING: Unified contract absent, falling back to heuristic")', 'derive_payload_contract 回退警告'),
            
            # test_plan_designer_node 逻辑
            ('if not (unified_contract and device_role and dp_contract):', 'test_plan_designer_node 前置检查'),
            ('topic_to_monitor = device_topics["pub"][0] if device_topics["pub"] else', 'test_plan_designer_node 主题选择'),
            
            # developer_node 验证逻辑
            ('try:', 'developer_node 异常处理开始'),
            ('_assert_topic_compliance', 'developer_node 合规性检查调用'),
            ('except ValueError as e:', 'developer_node 异常处理'),
        ]
        
        passed = 0
        for pattern, description in logic_checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 逻辑流程检查: {passed}/{len(logic_checks)} 项通过")
        return passed == len(logic_checks)
        
    except Exception as e:
        print(f"  ❌ 逻辑流程测试失败: {e}")
        return False

def analyze_existing_issues():
    """分析现有工作空间中的问题"""
    print("🔍 分析现有工作空间问题...")
    
    workspace_base = "app/temp_workspaces"
    if not os.path.exists(workspace_base):
        print("  ⚠️ 工作空间目录不存在")
        return True
    
    issues = {
        'placeholder_topics': [],
        'legacy_topics': [],
        'correct_topics': []
    }
    
    # 遍历工作空间
    for workspace in os.listdir(workspace_base):
        workspace_path = os.path.join(workspace_base, workspace)
        if not os.path.isdir(workspace_path):
            continue
        
        for device in os.listdir(workspace_path):
            device_path = os.path.join(workspace_path, device)
            if not os.path.isdir(device_path):
                continue
            
            code_file = os.path.join(device_path, 'src', 'app_main.ino')
            if os.path.exists(code_file):
                with open(code_file, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # 分析主题使用
                if '///data' in code:
                    issues['placeholder_topics'].append(f"{workspace}/{device}")
                elif '/sensor/value' in code or '/sensor/light' in code:
                    issues['legacy_topics'].append(f"{workspace}/{device}")
                else:
                    # 查找统一契约主题
                    unified_topics = re.findall(r'"/[^/]+/[^/]+/[^/]+"', code)
                    real_topics = [t for t in unified_topics if '///' not in t and '/sensor/' not in t]
                    if real_topics:
                        issues['correct_topics'].append((f"{workspace}/{device}", real_topics))
    
    # 输出分析结果
    print(f"  📊 现有工作空间分析:")
    print(f"    ❌ 占位符主题 (///data): {len(issues['placeholder_topics'])} 个")
    print(f"    ⚠️ 传统推导主题 (/sensor/*): {len(issues['legacy_topics'])} 个")
    print(f"    ✅ 统一契约主题: {len(issues['correct_topics'])} 个")
    
    if issues['placeholder_topics']:
        print("    占位符主题文件:")
        for file in issues['placeholder_topics'][:3]:  # 只显示前3个
            print(f"      - {file}")
        if len(issues['placeholder_topics']) > 3:
            print(f"      ... 还有 {len(issues['placeholder_topics']) - 3} 个")
    
    if issues['legacy_topics']:
        print("    传统推导主题文件:")
        for file in issues['legacy_topics'][:3]:  # 只显示前3个
            print(f"      - {file}")
        if len(issues['legacy_topics']) > 3:
            print(f"      ... 还有 {len(issues['legacy_topics']) - 3} 个")
    
    if issues['correct_topics']:
        print("    统一契约主题文件:")
        for file, topics in issues['correct_topics']:
            print(f"      - {file}: {topics}")
    
    # 这些是历史文件，不算测试失败
    return True

def main():
    """运行所有测试"""
    print("🚀 简单契约修复验证...\n")
    
    tests = [
        ("代码修改检查", test_code_modifications),
        ("函数签名检查", test_function_signatures),
        ("逻辑流程检查", test_logic_flow),
        ("现有问题分析", analyze_existing_issues)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 验证总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项验证通过")
    
    if passed >= 3:  # 前3项是关键的代码修复检查
        print("🎉 核心修复验证通过！")
        print("💡 提示: 现有工作空间文件是历史生成的，需要重新生成才能看到修复效果。")
        print("🔧 建议: 删除现有工作空间并重新运行工作流来验证修复效果。")
    else:
        print("⚠️ 核心修复验证失败，需要进一步检查。")

if __name__ == "__main__":
    main()
