#!/usr/bin/env python3
"""
验证修复效果 - 在重新运行工作流后使用此脚本验证
"""

import os
import re
import json

def check_latest_workspace():
    """检查最新工作空间的生成结果"""
    print("🔍 检查最新工作空间")
    print("=" * 40)
    
    workspace_base = "app/temp_workspaces"
    workspaces = [d for d in os.listdir(workspace_base) if d.startswith('wf-')]
    if not workspaces:
        print("❌ 没有找到工作空间")
        return False
    
    latest_workspace = sorted(workspaces)[-1]
    workspace_path = os.path.join(workspace_base, latest_workspace)
    print(f"📁 最新工作空间: {latest_workspace}")
    
    # 检查报警器和传感器设备
    devices = [d for d in os.listdir(workspace_path) if d.startswith('zygo_dev_')]
    print(f"📱 找到设备: {devices}")
    
    success_indicators = []
    
    for device in devices:
        device_path = os.path.join(workspace_path, device)
        code_path = os.path.join(device_path, 'src', 'app_main.ino')
        verify_path = os.path.join(device_path, 'run_verification.py')
        
        if not os.path.exists(code_path):
            continue
            
        with open(code_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        print(f"\n📄 分析设备: {device}")
        
        # 检查主题使用
        if '///data' in code:
            print("  ❌ 仍使用占位符主题 ///data")
        elif '/sensor/value' in code or '/sensor/light' in code:
            print("  ⚠️ 使用传统推导主题")
        else:
            # 查找真正的统一契约主题
            topics = re.findall(r'"/[^/]+/[^/]+/data"', code)
            real_topics = [t for t in topics if '///' not in t]
            if real_topics:
                print(f"  ✅ 使用统一契约主题: {real_topics}")
                success_indicators.append("correct_topics")
        
        # 检查键名使用
        if '"illumination":' in code:
            print("  ❌ 仍使用错误键名 illumination")
        elif '"illuminance_lux":' in code:
            print("  ✅ 使用DP契约键名 illuminance_lux")
            success_indicators.append("correct_keys")
        elif '"lux":' in code:
            print("  ⚠️ 使用简化键名 lux")
        
        # 检查验证脚本
        if os.path.exists(verify_path):
            with open(verify_path, 'r', encoding='utf-8') as f:
                verify_code = f.read()
            
            topic_match = re.search(r'TOPIC_TO_VERIFY = "([^"]+)"', verify_code)
            key_match = re.search(r'"([^"]+):"', verify_code)
            
            if topic_match and key_match:
                expected_topic = topic_match.group(1)
                expected_key = key_match.group(1)
                print(f"  📋 验证期望: {expected_topic} -> {expected_key}")
                
                if expected_topic != "/sensor/value" and expected_key != "illumination":
                    success_indicators.append("correct_verification")
    
    # 评估整体成功率
    print(f"\n📊 成功指标: {len(success_indicators)}/3")
    if len(success_indicators) >= 2:
        print("🎉 修复基本生效！")
        return True
    elif len(success_indicators) >= 1:
        print("⚠️ 修复部分生效，需要进一步调试")
        return False
    else:
        print("❌ 修复未生效，需要检查问题")
        return False

def provide_debugging_tips():
    """提供调试建议"""
    print("\n🔧 调试建议")
    print("=" * 40)
    
    print("如果修复仍未生效，请检查：")
    print("1. 🔄 确保完全重新启动了工作流（不是恢复旧的）")
    print("2. 📝 查看控制台输出中是否有：")
    print("   - 'Generated communication context for...'")
    print("   - 'Using unified contract...'")
    print("   - 'Contract violations detected'")
    print("3. 🎯 确认 plan_enrichment_node 生成了 unified_communication_contract")
    print("4. 🔍 检查 developer_node 是否接收到了通信契约信息")

def main():
    """主函数"""
    print("🚀 验证修复效果")
    print("=" * 50)
    
    try:
        success = check_latest_workspace()
        provide_debugging_tips()
        
        if success:
            print("\n✅ 修复验证成功！报警器应该能接收到光照数据了。")
            return 0
        else:
            print("\n⚠️ 修复验证失败，需要进一步调试。")
            return 1
            
    except Exception as e:
        print(f"\n❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 2

if __name__ == "__main__":
    exit(main())
