#!/usr/bin/env python3
"""
调试契约传递问题
"""

import os
import re

def analyze_debug_additions():
    """分析调试信息是否正确添加"""
    print("🔍 分析调试信息添加...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查调试信息是否添加
        debug_checks = [
            # developer_node 调试信息
            ('print(f"  -> [DEBUG] unified_contract exists: {bool(unified_contract)}")', 'developer_node 统一契约调试'),
            ('print(f"  -> [DEBUG] device_task exists: {bool(device_task)}")', 'developer_node 设备任务调试'),
            ('print(f"  -> [DEBUG] device_role: \'{device_role}\'")', 'developer_node 设备角色调试'),
            ('print(f"  -> [DEBUG] topic_map keys: {list(unified_contract.get(\'topic_map\', {}).keys())}")', 'developer_node 主题映射调试'),
            
            # test_plan_designer_node 调试信息
            ('print(f"  -> [DEBUG] unified_contract exists: {bool(unified_contract)}")', 'test_plan_designer_node 统一契约调试'),
            ('print(f"  -> [DEBUG] current_device_task exists: {bool(current_device_task)}")', 'test_plan_designer_node 设备任务调试'),
            
            # 改进的错误信息
            ('Available state keys: {list(state.keys())}', '详细错误信息 - state keys'),
            ('device_task: {device_task}', '详细错误信息 - device_task'),
        ]
        
        passed = 0
        for pattern, description in debug_checks:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 调试信息检查: {passed}/{len(debug_checks)} 项通过")
        return passed >= 6  # 至少要有主要的调试信息
        
    except Exception as e:
        print(f"  ❌ 调试信息分析失败: {e}")
        return False

def analyze_contract_flow():
    """分析契约流转路径"""
    print("🔍 分析契约流转路径...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查契约流转的关键点
        flow_points = [
            # 1. plan_enrichment_node 生成
            ('unified_communication_contract = {', 'plan_enrichment_node 生成'),
            ('return {.*"unified_communication_contract": unified_communication_contract', 'plan_enrichment_node 返回'),
            
            # 2. device_dispatcher_node 传递
            ('"unified_communication_contract": state.get(\'unified_communication_contract\')', 'device_dispatcher_node 传递'),
            ('"current_device_task": next_device_task,', 'device_dispatcher_node 设置当前任务'),
            
            # 3. 节点使用
            ('unified_contract = state.get(\'unified_communication_contract\', {})', '节点获取统一契约'),
            ('device_task = state.get(\'current_device_task\')', '节点获取当前任务'),
        ]
        
        passed = 0
        for pattern, description in flow_points:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 契约流转检查: {passed}/{len(flow_points)} 项通过")
        return passed == len(flow_points)
        
    except Exception as e:
        print(f"  ❌ 契约流转分析失败: {e}")
        return False

def check_potential_issues():
    """检查潜在问题"""
    print("🔍 检查潜在问题...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 1. 检查是否有地方清空了 unified_communication_contract
        clear_patterns = [
            r'"unified_communication_contract":\s*None',
            r'"unified_communication_contract":\s*{}',
            r'unified_communication_contract.*=.*None',
            r'unified_communication_contract.*=.*{}'
        ]
        
        for pattern in clear_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"可能清空统一契约的代码: {matches}")
        
        # 2. 检查是否有地方清空了 current_device_task
        task_clear_patterns = [
            r'"current_device_task":\s*None',
            r'current_device_task.*=.*None'
        ]
        
        for pattern in task_clear_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"可能清空设备任务的代码: {matches}")
        
        # 3. 检查状态重置逻辑
        reset_patterns = [
            r'states_to_reset.*=.*\[',
            r'update_dict\[.*\].*=.*None'
        ]
        
        reset_found = False
        for pattern in reset_patterns:
            if re.search(pattern, content):
                reset_found = True
                break
        
        if reset_found:
            print(f"  ⚠️ 发现状态重置逻辑，可能影响契约传递")
            # 查找具体的重置内容
            reset_section = re.search(r'states_to_reset = \[(.*?)\]', content, re.DOTALL)
            if reset_section:
                reset_items = reset_section.group(1)
                if 'unified_communication_contract' in reset_items:
                    issues.append("states_to_reset 包含 unified_communication_contract")
                if 'current_device_task' in reset_items:
                    issues.append("states_to_reset 包含 current_device_task")
        
        if issues:
            print(f"  ❌ 发现 {len(issues)} 个潜在问题:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print(f"  ✅ 未发现明显的潜在问题")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"  ❌ 潜在问题检查失败: {e}")
        return False

def suggest_next_steps():
    """建议下一步操作"""
    print("💡 建议下一步操作...")
    
    print("  1. 重新运行工作流，观察调试输出")
    print("  2. 检查哪个节点首先报告缺失契约")
    print("  3. 如果是 developer_node，查看调试信息:")
    print("     - unified_contract exists: 应该是 True")
    print("     - device_task exists: 应该是 True") 
    print("     - device_role: 应该不为空")
    print("     - topic_map keys: 应该包含设备角色")
    print("  4. 如果调试信息显示契约存在但仍然失败，可能是:")
    print("     - 契约内容为空字典 {}")
    print("     - device_role 不在 topic_map 中")
    print("     - topic_map 本身为空")
    print("  5. 根据调试输出进一步定位问题")

def main():
    """运行所有分析"""
    print("🚀 调试契约传递问题...\n")
    
    tests = [
        ("调试信息添加", analyze_debug_additions),
        ("契约流转路径", analyze_contract_flow),
        ("潜在问题检查", check_potential_issues)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 分析总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项分析通过")
    
    suggest_next_steps()

if __name__ == "__main__":
    main()
