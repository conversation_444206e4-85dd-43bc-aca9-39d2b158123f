#!/usr/bin/env python3
"""
调试状态传递问题
"""

import os
import re

def analyze_state_flow():
    """分析状态在工作流中的传递"""
    print("🔍 分析状态传递...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有返回 unified_communication_contract 的地方
        print("📤 查找生成 unified_communication_contract 的节点:")
        
        # 1. plan_enrichment_node 生成
        if '"unified_communication_contract": unified_communication_contract' in content:
            print("  ✅ plan_enrichment_node 生成并返回 unified_communication_contract")
        else:
            print("  ❌ plan_enrichment_node 未返回 unified_communication_contract")
        
        # 2. device_dispatcher_node 传递
        if 'update_dict[\'unified_communication_contract\'] = unified_contract' in content:
            print("  ✅ device_dispatcher_node 强制传递 unified_communication_contract")
        else:
            print("  ❌ device_dispatcher_node 未强制传递 unified_communication_contract")
        
        # 查找所有可能清空或覆盖状态的地方
        print("\n🗑️ 查找可能清空状态的地方:")
        
        # 1. 检查返回空字典的节点
        empty_returns = re.findall(r'return\s*{\s*}', content)
        print(f"  📊 发现 {len(empty_returns)} 处返回空字典")
        
        # 2. 检查可能覆盖状态的地方
        state_overwrites = re.findall(r'return\s*{[^}]*}', content)
        print(f"  📊 发现 {len(state_overwrites)} 处返回状态字典")
        
        # 3. 检查特定的问题模式
        problematic_patterns = [
            (r'return\s*{\s*"[^"]*":\s*[^}]*}\s*(?!.*unified_communication_contract)', '返回字典但不包含统一契约'),
            (r'"unified_communication_contract":\s*None', '明确设置统一契约为 None'),
            (r'"unified_communication_contract":\s*{}', '设置统一契约为空字典'),
        ]
        
        issues_found = []
        for pattern, description in problematic_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                issues_found.append((description, len(matches)))
        
        if issues_found:
            print("  ⚠️ 发现潜在问题:")
            for description, count in issues_found:
                print(f"    - {description}: {count} 处")
        else:
            print("  ✅ 未发现明显的状态覆盖问题")
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f"  ❌ 状态传递分析失败: {e}")
        return False

def check_workflow_path():
    """检查工作流路径是否正确"""
    print("🔍 检查工作流路径...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键路径
        path_checks = [
            ('workflow.add_edge("plan_enrichment_node", "device_dispatcher")', 'plan_enrichment → device_dispatcher'),
            ('workflow.add_conditional_edges.*"device_dispatcher"', 'device_dispatcher 条件边'),
            ('"continue_to_development": "dp_designer"', 'device_dispatcher → dp_designer'),
            ('workflow.add_edge.*"dp_designer"', 'dp_designer 出边'),
        ]
        
        passed = 0
        for pattern, description in path_checks:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n  📊 工作流路径检查: {passed}/{len(path_checks)} 项通过")
        return passed == len(path_checks)
        
    except Exception as e:
        print(f"  ❌ 工作流路径检查失败: {e}")
        return False

def find_missing_debug_output():
    """查找缺失的调试输出"""
    print("🔍 查找缺失的调试输出...")
    
    expected_debug_outputs = [
        ("device_dispatcher_node: queue length =", "device_dispatcher_node 队列调试"),
        ("Explicitly passing unified_communication_contract", "device_dispatcher_node 契约传递调试"),
        ("dp_designer_node: unified_contract exists:", "dp_designer_node 契约调试"),
    ]
    
    print("  📋 预期的调试输出:")
    for output, description in expected_debug_outputs:
        print(f"    - {description}: '{output}'")
    
    print("\n  💡 如果这些调试输出没有出现在日志中，说明:")
    print("    1. 对应的节点没有被调用")
    print("    2. 或者节点被调用但条件不满足")
    print("    3. 或者有异常导致调试输出被跳过")
    
    return True

def suggest_next_debugging_steps():
    """建议下一步调试步骤"""
    print("💡 建议下一步调试步骤...")
    
    print("  1. 🔍 检查完整的工作流日志:")
    print("     - 查找 'plan_enrichment_node' 的输出")
    print("     - 查找 'device_dispatcher_node' 的调试信息")
    print("     - 查找 'dp_designer_node' 的调试信息")
    
    print("  2. 🎯 重点关注:")
    print("     - plan_enrichment_node 是否输出了 'Established unified communication contract'")
    print("     - device_dispatcher_node 是否输出了 'queue length = X'")
    print("     - device_dispatcher_node 是否输出了契约传递信息")
    
    print("  3. 🔧 如果 device_dispatcher_node 没有被调用:")
    print("     - 检查 check_device_queue 函数的逻辑")
    print("     - 检查工作流路由是否正确")
    
    print("  4. 🔧 如果 device_dispatcher_node 被调用但契约丢失:")
    print("     - 检查 persistent_state 的构建")
    print("     - 检查是否有其他地方覆盖了状态")
    
    print("  5. 🧪 临时解决方案:")
    print("     - 在 developer_node 中添加 fallback 逻辑")
    print("     - 或者在 dp_designer_node 中重新生成契约")

def main():
    """运行所有分析"""
    print("🚀 调试状态传递问题...\n")
    
    tests = [
        ("状态传递分析", analyze_state_flow),
        ("工作流路径检查", check_workflow_path),
        ("缺失调试输出分析", find_missing_debug_output),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 分析总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项分析通过")
    
    suggest_next_debugging_steps()
    
    print("\n🔧 立即行动建议:")
    print("  1. 重新运行工作流，仔细观察完整的日志输出")
    print("  2. 查找是否有 'device_dispatcher_node' 的调试信息")
    print("  3. 如果没有，说明工作流路由有问题")
    print("  4. 如果有但契约丢失，说明状态传递有问题")

if __name__ == "__main__":
    main()
