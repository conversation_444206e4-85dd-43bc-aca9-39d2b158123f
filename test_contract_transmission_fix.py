#!/usr/bin/env python3
"""
测试统一通信契约传递修复
"""

import os
import re

def test_contract_transmission_fixes():
    """测试契约传递修复"""
    print("🧪 测试契约传递修复...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes = [
            # device_artifact_generator_node 修复
            ('result[\'unified_communication_contract\'] = unified_contract', 'device_artifact_generator 传递契约'),
            ('device_artifact_generator: Passing unified_communication_contract', 'device_artifact_generator 调试信息'),
            ('device_artifact_generator: unified_communication_contract is missing', 'device_artifact_generator 缺失警告'),
            
            # module_architect_node 修复
            ('result[\'unified_communication_contract\'] = unified_contract', 'module_architect 传递契约'),
            ('module_architect: Passing unified_communication_contract', 'module_architect 调试信息'),
            ('module_architect: unified_communication_contract is missing', 'module_architect 缺失警告'),
            
            # 错误情况下的契约传递
            ('# V6 修复: 错误情况下也要传递统一通信契约', 'module_architect 错误处理'),
        ]
        
        passed = 0
        failed = []
        
        for pattern, description in fixes:
            if pattern in content:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")
                failed.append(description)
        
        print(f"\n  📊 契约传递修复检查: {passed}/{len(fixes)} 项通过")
        
        if failed:
            print("  ❌ 失败的检查项:")
            for item in failed:
                print(f"    - {item}")
        
        return len(failed) == 0
        
    except Exception as e:
        print(f"  ❌ 契约传递修复测试失败: {e}")
        return False

def analyze_workflow_path():
    """分析完整的工作流路径"""
    print("🔍 分析完整的工作流路径...")
    
    workflow_path = [
        "plan_enrichment_node",
        "device_dispatcher",
        "dp_designer", 
        "device_artifact_generator",
        "module_architect",
        "module_dispatcher",
        "api_designer",
        "developer"
    ]
    
    print("  📋 关键工作流路径:")
    for i, node in enumerate(workflow_path):
        arrow = " → " if i < len(workflow_path) - 1 else ""
        print(f"    {i+1}. {node}{arrow}")
    
    print("\n  🎯 契约传递检查点:")
    critical_nodes = [
        ("plan_enrichment_node", "生成 unified_communication_contract"),
        ("device_dispatcher", "传递 unified_communication_contract"),
        ("device_artifact_generator", "传递 unified_communication_contract"),
        ("module_architect", "传递 unified_communication_contract"),
        ("developer", "使用 unified_communication_contract")
    ]
    
    for node, action in critical_nodes:
        print(f"    - {node}: {action}")
    
    return True

def check_remaining_issues():
    """检查可能剩余的问题"""
    print("🔍 检查可能剩余的问题...")
    
    try:
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找其他可能不传递契约的节点
        return_patterns = re.findall(r'return\s*{\s*"[^"]*":[^}]*}', content)
        
        # 检查是否还有节点只返回部分状态
        problematic_returns = []
        for pattern in return_patterns:
            if 'unified_communication_contract' not in pattern and len(pattern) > 50:
                # 这是一个返回多个字段但不包含契约的返回语句
                problematic_returns.append(pattern[:100] + "...")
        
        if problematic_returns:
            print(f"  ⚠️ 发现 {len(problematic_returns)} 个可能有问题的返回语句:")
            for i, ret in enumerate(problematic_returns[:3]):  # 只显示前3个
                print(f"    {i+1}. {ret}")
            if len(problematic_returns) > 3:
                print(f"    ... 还有 {len(problematic_returns) - 3} 个")
        else:
            print(f"  ✅ 未发现明显的问题返回语句")
        
        # 检查是否有其他关键节点
        key_nodes = [
            "module_dispatcher_node",
            "api_designer_node", 
            "dp_designer_node"
        ]
        
        missing_checks = []
        for node in key_nodes:
            if f"def {node}" in content:
                # 检查这个节点是否传递契约
                node_section = re.search(f'def {node}.*?(?=def |\Z)', content, re.DOTALL)
                if node_section and 'unified_communication_contract' not in node_section.group():
                    missing_checks.append(node)
        
        if missing_checks:
            print(f"  ⚠️ 以下节点可能也需要检查契约传递:")
            for node in missing_checks:
                print(f"    - {node}")
        else:
            print(f"  ✅ 关键节点都已检查契约传递")
        
        return len(problematic_returns) < 5 and len(missing_checks) == 0
        
    except Exception as e:
        print(f"  ❌ 剩余问题检查失败: {e}")
        return False

def suggest_verification_steps():
    """建议验证步骤"""
    print("💡 建议验证步骤...")
    
    print("  1. 🔍 重新运行工作流，观察调试输出:")
    print("     - 查找 'plan_enrichment_node: Established unified communication contract'")
    print("     - 查找 'device_dispatcher_node: Explicitly passing unified_communication_contract'")
    print("     - 查找 'device_artifact_generator: Passing unified_communication_contract'")
    print("     - 查找 'module_architect: Passing unified_communication_contract'")
    print("     - 查找 'dp_designer_node: unified_contract exists: True'")
    
    print("  2. 🎯 关键验证点:")
    print("     - developer_node 应该输出 'unified_contract exists: True'")
    print("     - 不应该再出现 'Missing unified_communication_contract' 错误")
    
    print("  3. 🔧 如果仍有问题:")
    print("     - 检查是否有其他节点在路径中")
    print("     - 检查 module_dispatcher_node 是否传递契约")
    print("     - 检查 api_designer_node 是否传递契约")
    
    print("  4. 🎉 成功标志:")
    print("     - 工作流不再崩溃")
    print("     - developer_node 收到 unified_communication_contract")
    print("     - 生成的代码使用正确的 MQTT 主题")

def main():
    """运行所有测试"""
    print("🚀 测试统一通信契约传递修复...\n")
    
    tests = [
        ("契约传递修复", test_contract_transmission_fixes),
        ("工作流路径分析", analyze_workflow_path),
        ("剩余问题检查", check_remaining_issues)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 测试总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed >= 2:  # 前2项是关键的修复检查
        print("🎉 契约传递修复成功！")
        print("💡 现在关键节点都会传递 unified_communication_contract。")
        print("🔧 建议: 重新运行工作流来验证修复效果。")
    else:
        print("⚠️ 修复可能不完整，需要进一步检查。")
    
    suggest_verification_steps()

if __name__ == "__main__":
    main()
