#!/usr/bin/env python3
"""
直接验证修复后的函数是否正确工作
"""

import os
import sys
import json

# 添加路径以便导入
sys.path.insert(0, os.path.join(os.getcwd(), 'app'))

def test_derive_payload_contract_with_unified_contract():
    """测试 derive_payload_contract 在有统一契约时的行为"""
    print("🧪 测试 derive_payload_contract (有统一契约)")
    
    try:
        from langgraph_def.graph_builder import derive_payload_contract
        
        # 模拟完整的状态
        state = {
            'unified_communication_contract': {
                'topic_map': {
                    '光照采集端': {
                        'pub': ['/smart_light_system/light_collector/data'],
                        'sub': []
                    },
                    '报警器': {
                        'pub': [],
                        'sub': ['/smart_light_system/light_collector/data']
                    }
                }
            },
            'current_device_task': {
                'device_role': '光照采集端'
            },
            'device_dp_contract': [
                {'code': 'illuminance', 'name': '光照强度'}
            ]
        }
        
        result = derive_payload_contract(state)
        
        print(f"  结果: {result}")
        
        # 验证结果
        expected_topic = '/smart_light_system/light_collector/data'
        expected_key = 'illuminance'
        
        if result['topic'] == expected_topic and result['json_key'] == expected_key:
            print("  ✅ 光照采集端测试通过")
        else:
            print(f"  ❌ 光照采集端测试失败: 期望 topic={expected_topic}, key={expected_key}")
            return False
        
        # 测试报警器
        state['current_device_task']['device_role'] = '报警器'
        result2 = derive_payload_contract(state)
        
        print(f"  报警器结果: {result2}")
        
        if result2['topic'] == expected_topic and result2['json_key'] == expected_key:
            print("  ✅ 报警器测试通过")
            return True
        else:
            print(f"  ❌ 报警器测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_derive_payload_contract_fallback():
    """测试 derive_payload_contract 在没有统一契约时的回退行为"""
    print("🧪 测试 derive_payload_contract (回退模式)")
    
    try:
        from langgraph_def.graph_builder import derive_payload_contract
        
        # 模拟没有统一契约的状态
        state = {
            'current_device_task': {
                'device_role': '光照传感器',
                'peripherals': [
                    {'name': 'BH1750', 'model': 'BH1750'}
                ]
            }
        }
        
        result = derive_payload_contract(state)
        
        print(f"  回退结果: {result}")
        
        # 应该回退到传统推导
        if result['topic'] and result['json_key']:
            print("  ✅ 回退模式测试通过")
            return True
        else:
            print("  ❌ 回退模式测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 回退测试失败: {e}")
        return False

def test_topic_compliance():
    """测试主题合规性检查"""
    print("🧪 测试主题合规性检查")
    
    try:
        from langgraph_def.graph_builder import _assert_topic_compliance
        
        # 测试正确的代码
        good_code = '''
        localMqttClient.publish("/smart_light_system/light_collector/data", payload);
        '''
        
        contract = {
            'topic_map': {
                'sensor': {
                    'pub': ['/smart_light_system/light_collector/data'],
                    'sub': []
                }
            }
        }
        
        try:
            _assert_topic_compliance(good_code, contract)
            print("  ✅ 正确代码通过检查")
        except ValueError as e:
            print(f"  ❌ 正确代码被错误拒绝: {e}")
            return False
        
        # 测试错误的代码
        bad_code = '''
        localMqttClient.publish("///data", payload);
        '''
        
        try:
            _assert_topic_compliance(bad_code, contract)
            print("  ❌ 错误代码应该被拒绝但通过了")
            return False
        except ValueError:
            print("  ✅ 错误代码被正确拒绝")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 合规性测试失败: {e}")
        return False

def test_communication_context_generation():
    """测试通信上下文生成是否包含完整契约"""
    print("🧪 测试通信上下文生成")
    
    try:
        # 检查 graph_builder.py 中的相关代码
        with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            ('<CommunicationContract>', '通信契约标签'),
            ('json.dumps(unified_contract', '契约JSON序列化'),
            ('_assert_topic_compliance', '主题合规性检查函数'),
            ('unified_communication_contract.*persistent_state', '统一契约传递')
        ]
        
        passed = 0
        for pattern, description in checks:
            if pattern in content:
                print(f"  ✅ {description} 存在")
                passed += 1
            else:
                print(f"  ❌ {description} 缺失")
        
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ 上下文生成测试失败: {e}")
        return False

def main():
    """运行所有验证测试"""
    print("🚀 验证通信契约修复...\n")
    
    tests = [
        ("derive_payload_contract (统一契约)", test_derive_payload_contract_with_unified_contract),
        ("derive_payload_contract (回退模式)", test_derive_payload_contract_fallback),
        ("主题合规性检查", test_topic_compliance),
        ("通信上下文生成", test_communication_context_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 验证总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项验证通过")
    
    if passed == len(results):
        print("🎉 所有验证通过！修复代码正确工作。")
        print("💡 提示: 需要重新生成设备代码才能看到修复效果。")
    else:
        print("⚠️ 部分验证失败，需要进一步修复。")

if __name__ == "__main__":
    main()
