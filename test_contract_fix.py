#!/usr/bin/env python3
"""
测试脚本：验证通信契约修复的效果
检查是否所有节点都使用统一的通信契约，而不是各自的拍脑袋逻辑
"""

import os
import re
import json
from pathlib import Path

def test_derive_payload_contract():
    """测试 derive_payload_contract 是否优先使用统一契约"""
    print("🧪 测试 derive_payload_contract 函数...")
    
    # 模拟状态
    state = {
        'unified_communication_contract': {
            'topic_map': {
                '光照采集端': {
                    'pub': ['/smart_light_system/light_collector/data'],
                    'sub': []
                },
                '报警器': {
                    'pub': [],
                    'sub': ['/smart_light_system/light_collector/data']
                }
            }
        },
        'current_device_task': {
            'device_role': '光照采集端'
        },
        'device_dp_contract': [
            {'code': 'illuminance', 'name': '光照强度'}
        ]
    }
    
    # 导入并测试函数
    import sys
    sys.path.insert(0, os.path.join(os.getcwd(), 'app'))
    from langgraph_def.graph_builder import derive_payload_contract
    
    result = derive_payload_contract(state)
    
    expected_topic = '/smart_light_system/light_collector/data'
    expected_key = 'illuminance'
    
    assert result['topic'] == expected_topic, f"期望主题 {expected_topic}, 实际 {result['topic']}"
    assert result['json_key'] == expected_key, f"期望键名 {expected_key}, 实际 {result['json_key']}"
    
    print("  ✅ derive_payload_contract 测试通过")
    return True

def test_communication_contract_injection():
    """测试通信契约是否被正确注入到 developer_node 的 prompt 中"""
    print("🧪 测试通信契约注入...")
    
    # 检查 graph_builder.py 中的 communication_context 构建
    with open('app/langgraph_def/graph_builder.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含 CommunicationContract 标签
    if '<CommunicationContract>' in content and 'json.dumps(unified_contract' in content:
        print("  ✅ 通信契约正确注入到 prompt 中")
        return True
    else:
        print("  ❌ 通信契约注入不完整")
        return False

def test_topic_compliance_check():
    """测试代码合规性检查函数"""
    print("🧪 测试主题合规性检查...")
    
    # 导入检查函数
    import sys
    sys.path.insert(0, os.path.join(os.getcwd(), 'app'))
    from langgraph_def.graph_builder import _assert_topic_compliance
    
    # 测试用例1：包含正确主题的代码
    good_code = '''
    localMqttClient.publish("/smart_light_system/light_collector/data", payload);
    '''
    
    contract = {
        'topic_map': {
            'sensor': {
                'pub': ['/smart_light_system/light_collector/data'],
                'sub': []
            }
        }
    }
    
    try:
        _assert_topic_compliance(good_code, contract)
        print("  ✅ 正确代码通过合规性检查")
    except ValueError:
        print("  ❌ 正确代码未通过合规性检查")
        return False
    
    # 测试用例2：包含错误主题的代码
    bad_code = '''
    localMqttClient.publish("///data", payload);
    '''
    
    try:
        _assert_topic_compliance(bad_code, contract)
        print("  ❌ 错误代码应该被拒绝但通过了检查")
        return False
    except ValueError:
        print("  ✅ 错误代码被正确拒绝")
    
    return True

def analyze_existing_workspace():
    """分析现有工作空间中的代码，检查主题使用情况"""
    print("🔍 分析现有工作空间...")
    
    workspace_base = Path("app/temp_workspaces")
    if not workspace_base.exists():
        print("  ⚠️ 工作空间目录不存在")
        return True
    
    findings = {
        'correct_topics': [],
        'placeholder_topics': [],
        'legacy_topics': []
    }
    
    # 遍历所有工作空间
    for workspace in workspace_base.iterdir():
        if not workspace.is_dir():
            continue
            
        # 查找设备目录
        for device_dir in workspace.iterdir():
            if not device_dir.is_dir():
                continue
                
            code_file = device_dir / 'src' / 'app_main.ino'
            if code_file.exists():
                with open(code_file, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # 检查主题类型
                if '///data' in code:
                    findings['placeholder_topics'].append(str(code_file))
                elif '/sensor/value' in code or '/sensor/light' in code:
                    findings['legacy_topics'].append(str(code_file))
                else:
                    # 查找统一契约主题
                    unified_topics = re.findall(r'"/[^/]+/[^/]+/[^/]+"', code)
                    if unified_topics:
                        findings['correct_topics'].append((str(code_file), unified_topics))
    
    # 输出分析结果
    print(f"  📊 分析结果:")
    print(f"    ✅ 使用统一契约主题: {len(findings['correct_topics'])} 个文件")
    print(f"    ❌ 使用占位符主题: {len(findings['placeholder_topics'])} 个文件")
    print(f"    ⚠️ 使用传统推导主题: {len(findings['legacy_topics'])} 个文件")
    
    if findings['correct_topics']:
        print("    正确使用统一契约的文件:")
        for file_path, topics in findings['correct_topics']:
            print(f"      {file_path}: {topics}")
    
    return len(findings['placeholder_topics']) == 0 and len(findings['legacy_topics']) == 0

def main():
    """运行所有测试"""
    print("🚀 开始测试通信契约修复效果...\n")
    
    tests = [
        ("derive_payload_contract 函数", test_derive_payload_contract),
        ("通信契约注入", test_communication_contract_injection),
        ("主题合规性检查", test_topic_compliance_check),
        ("现有工作空间分析", analyze_existing_workspace)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ 测试 '{test_name}' 失败: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("📋 测试总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！通信契约修复成功。")
    else:
        print("⚠️ 部分测试失败，需要进一步修复。")

if __name__ == "__main__":
    main()
